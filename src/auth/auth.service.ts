import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { User } from '@prisma/client';

import { JwtPayload } from '../common/interfaces/jwt-payload.interface';
import { EmailService } from '../email/email.service';
import { UsersService } from '../users/users.service';
import { RegisterDto } from './dto/register.dto';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private emailService: EmailService,
  ) {}

  async forgotPassword(email: string) {
    const resetToken = await this.usersService.setResetPasswordToken(email);
    await this.emailService.sendPasswordResetEmail(email, resetToken);

    return {
      message: '密码重置邮件已发送，请检查您的邮箱',
    };
  }

  async getProfile(userId: string) {
    const user = await this.usersService.findOne(userId);
    return {
      createdAt: user.createdAt,
      email: user.email,
      id: user.id,
      isEmailVerified: user.isEmailVerified,
      name: user.name,
      updatedAt: user.updatedAt,
    };
  }

  login(user: User) {
    const payload: JwtPayload = { email: user.email, sub: user.id };
    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }

  async register(registerDto: RegisterDto) {
    const user = await this.usersService.create(registerDto);

    // 发送验证邮件
    if (user.emailVerificationToken) {
      await this.emailService.sendVerificationEmail(
        user.email,
        user.emailVerificationToken,
      );
    }

    // 返回时排除敏感信息
    const { emailVerificationToken, ...safeUser } = user;
    return {
      message: '注册成功，请检查您的邮箱并验证邮箱地址',
      user: safeUser,
    };
  }

  async resetPassword(token: string, newPassword: string) {
    const user = await this.usersService.resetPassword(token, newPassword);

    return {
      message: '密码重置成功',
      user: {
        email: user.email,
        id: user.id,
        isEmailVerified: user.isEmailVerified,
        name: user.name,
      },
    };
  }

  async updateProfile(userId: string, updateData: { name?: string }) {
    const user = await this.usersService.update(userId, updateData);
    return {
      createdAt: user.createdAt,
      email: user.email,
      id: user.id,
      isEmailVerified: user.isEmailVerified,
      name: user.name,
      updatedAt: user.updatedAt,
    };
  }

  async validateUser(email: string, password: string) {
    const user = await this.usersService.findByEmailAndPassword(
      email,
      password,
    );

    return user;
  }

  async verifyEmail(token: string) {
    const user = await this.usersService.verifyEmail(token);
    return {
      message: '邮箱验证成功',
      user: {
        email: user.email,
        id: user.id,
        isEmailVerified: user.isEmailVerified,
        name: user.name,
      },
    };
  }
}
