import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';

import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UsersService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createUserDto: CreateUserDto) {
    const { email, name, password } = createUserDto;

    // 检查邮箱是否已存在
    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictException('该邮箱已被注册');
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);

    // 生成邮箱验证token
    const emailVerificationToken = this.generateToken();

    const user = await this.prisma.user.create({
      data: {
        email,
        emailVerificationToken,
        name,
        password: hashedPassword,
      },
    });

    // 返回用户信息和验证token（用于发送邮件）
    return {
      ...user,
      emailVerificationToken,
    };
  }

  async findAll() {
    return this.prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByEmail(email: string, includePassword = false) {
    // 根据参数决定是否返回密码字段
    if (includePassword) {
      return this.prisma.user.findUnique({
        where: { email },
      });
    }

    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  async findByEmailAndPassword(email: string, password: string) {
    const user = await this.prisma.user.findUnique({
      omit: {
        password: false,
      },
      where: { email },
    });

    if (!user || !(await this.validatePassword(password, user.password))) {
      return null;
    }

    return {
      ...user,
      password: undefined,
    };
  }

  async findByEmailVerificationToken(token: string) {
    // 这个方法只需要返回默认的用户信息，不需要敏感字段
    return this.prisma.user.findUnique({
      where: { emailVerificationToken: token },
    });
  }

  async findByResetPasswordToken(token: string) {
    // 这个方法只需要返回默认的用户信息，不需要敏感字段
    return this.prisma.user.findFirst({
      where: {
        resetPasswordExpires: {
          gt: new Date(),
        },
        resetPasswordToken: token,
      },
    });
  }

  async findOne(id: string) {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  async remove(id: string) {
    await this.findOne(id); // 确保用户存在

    return this.prisma.user.delete({
      where: { id },
    });
  }

  async resetPassword(token: string, newPassword: string) {
    const user = await this.findByResetPasswordToken(token);

    if (!user) {
      throw new NotFoundException('无效或已过期的重置token');
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    return this.prisma.user.update({
      data: {
        password: hashedPassword,
        resetPasswordExpires: null,
        resetPasswordToken: null,
      },
      where: { id: user.id },
    });
  }

  async setResetPasswordToken(email: string): Promise<string> {
    const user = await this.findByEmail(email);

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const resetToken = this.generateToken();
    const resetExpires = new Date(Date.now() + 3600000); // 1小时后过期

    await this.prisma.user.update({
      data: {
        resetPasswordExpires: resetExpires,
        resetPasswordToken: resetToken,
      },
      where: { id: user.id },
    });

    return resetToken;
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    await this.findOne(id); // 确保用户存在

    return this.prisma.user.update({
      data: updateUserDto,
      where: { id },
    });
  }

  async validatePassword(
    password: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  async verifyEmail(token: string) {
    const user = await this.findByEmailVerificationToken(token);

    if (!user) {
      throw new NotFoundException('无效的验证token');
    }

    return this.prisma.user.update({
      data: {
        emailVerificationToken: null,
        isEmailVerified: true,
      },
      where: { id: user.id },
    });
  }

  private generateToken(): string {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  }
}
