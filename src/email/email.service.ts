import { Injectable } from '@nestjs/common';
import { ResendService } from 'nestjs-resend';

@Injectable()
export class EmailService {
  constructor(private readonly resendService: ResendService) {}

  async sendPasswordResetEmail(email: string, token: string) {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;

    return this.resendService.send({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>重置您的密码</h2>
          <p>您请求重置密码。请点击下面的链接来重置您的密码：</p>
          <a href="${resetUrl}" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
            重置密码
          </a>
          <p>如果您无法点击按钮，请复制以下链接到浏览器中：</p>
          <p>${resetUrl}</p>
          <p>此链接将在1小时后过期。</p>
          <p>如果您没有请求重置密码，请忽略此邮件。</p>
        </div>
      `,
      subject: '重置您的密码',
      to: email,
    });
  }

  async sendVerificationEmail(email: string, token: string) {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;

    return this.resendService.send({
      from: process.env.FROM_EMAIL || '<EMAIL>',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>验证您的邮箱地址</h2>
          <p>感谢您注册我们的服务！请点击下面的链接来验证您的邮箱地址：</p>
          <a href="${verificationUrl}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
            验证邮箱
          </a>
          <p>如果您无法点击按钮，请复制以下链接到浏览器中：</p>
          <p>${verificationUrl}</p>
          <p>此链接将在24小时后过期。</p>
        </div>
      `,
      subject: '验证您的邮箱地址',
      to: email,
    });
  }
}
