{"name": "nestjs-boilerplate", "version": "0.0.1", "private": true, "description": "", "author": "", "license": "UNLICENSED", "scripts": {"build": "nest build", "build:prod": "SWCRC=.swcrc.prod nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "pnpm oxlint \"{src,apps,libs,test}/**/*.ts\" --fix && eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "vitest run", "test:watch": "vitest", "test:cov": "vitest run --coverage", "test:debug": "vitest --inspect-brk", "test:e2e": "vitest run --config ./vitest.e2e.config.ts"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@prisma/client": "^6.13.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "nestjs-resend": "^1.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prisma": "^6.13.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@antfu/eslint-config": "^5.0.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.7.8", "@swc/core": "^1.13.3", "@swc/jest": "^0.2.39", "@types/bcrypt": "^6.0.0", "@types/express": "^5.0.0", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-oxlint": "^1.9.0", "eslint-plugin-perfectionist": "^4.15.0", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "oxlint": "^1.9.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "unplugin-swc": "^1.5.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}