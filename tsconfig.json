{"compilerOptions": {"incremental": true, "target": "ES2023", "emitDecoratorMetadata": true, "experimentalDecorators": true, "baseUrl": "./", "module": "nodenext", "moduleResolution": "nodenext", "paths": {"~/*": ["./src/*"]}, "resolvePackageJsonExports": true, "types": ["vitest", "vitest/globals"], "strictBindCallApply": true, "strictNullChecks": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "declaration": true, "outDir": "./dist", "removeComments": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}}